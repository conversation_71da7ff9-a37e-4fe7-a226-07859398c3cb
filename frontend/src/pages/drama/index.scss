.page-drama {
  min-height: 100vh;
  background-color: #ffffff;
  padding-bottom: 160rpx; // 增加底部间距，为固定分享按钮留空间
  position: relative;
}

.main-content {
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

// 短剧信息区域
.drama-info {
  background: white;
  padding: 32rpx;
}

.drama-profile {
  display: flex;
  gap: 32rpx;
}

.drama-cover {
  width: 33.33%; // 占据1/3宽度
  aspect-ratio: 3/4; // 4比3竖图
  background-color: #e5e7eb;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  overflow: hidden;

  .cover-text {
    color: #6b7280;
    font-size: 32rpx;
  }

  .cover-image {
    width: 100%;
    height: 100%;
    border-radius: 16rpx;
  }
}

.drama-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.drama-title {
  font-size: 36rpx; // 调大一个字号
  font-weight: bold;
  color: #1f2937;
}

.drama-producer,
.drama-episodes,
.drama-read-count {
  font-size: 28rpx; // 统一字号
  color: #6b7280;
}

// 主演区域
.drama-cast-section {
  margin-bottom: 8rpx;
}

.cast-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.cast-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: #f3f4f6;
  border-radius: 24rpx;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #e5e7eb;
    transform: translateY(-2rpx);
  }
}

.actor-avatar {
  width: 32rpx;
  height: 32rpx;
  background: #d1d5db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  .avatar-text {
    font-size: 16rpx;
    color: #6b7280;
  }

  .avatar-image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
}

.actor-name {
  font-size: 24rpx;
  color: #374151;
  font-weight: 500;
}

.drama-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 8rpx;
}

.drama-tag {
  color: #3730a3;
  font-size: 24rpx;
  font-weight: 500;
  margin-right: 16rpx;
}

// 推荐理由区域
// 简介区域
.description-section {
  background: white;
  padding: 32rpx;
}

.description-text {
  font-size: 28rpx; // 与热度字号统一
  color: #6b7280;
  line-height: 1.6;
}

// 推荐理由
.recommendation-section {
  background: white;
  padding: 32rpx;
}

.recommendation-text {
  font-size: 28rpx; // 与热度字号统一
  color: #6b7280;
  line-height: 1.6;
}

// 演员区域
.actors-section {
  background: white;
  padding: 32rpx;
}

.actors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 32rpx;
  margin-top: 16rpx;
}

.actor-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  cursor: pointer;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.02);
  }
}

.actor-avatar-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.actor-avatar-large {
  width: 120rpx;
  height: 120rpx;
  background: #d1d5db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-bottom: 8rpx;

  .avatar-text-large {
    font-size: 24rpx;
    color: #6b7280;
  }

  .avatar-image-large {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
}

.follow-button {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 4rpx 12rpx 0 rgba(99, 102, 241, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

.follow-text {
  font-size: 20rpx;
  color: white;
  font-weight: 500;
}

.actor-name-large {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
  text-align: center;
}

// 评论区域
.comment-section {
  background: white;
  padding: 32rpx;
}

.comment-input {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 32rpx;
  padding: 12rpx 20rpx; // 减少内边距，降低高度
  background-color: #f9fafb;
  border-radius: 40rpx; // 减少圆角，配合较低的高度
  border: 2rpx solid #e5e7eb;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #6366f1;
    background-color: #f0f9ff;
  }
}

.comment-field-placeholder {
  flex: 1;

  .placeholder-text {
    color: #9ca3af;
    font-size: 28rpx;
  }
}

.comment-submit {
  background-color: #4f46e5;
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 9999px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: #4338ca;
  }

  &:active {
    transform: scale(0.95);
  }
}

.submit-text {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
}

.comment-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.comment-item {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
}

.comment-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: #d1d5db;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  .avatar-text {
    color: #6b7280;
    font-size: 20rpx;
  }
}

.comment-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.comment-author {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
}

.comment-text {
  font-size: 28rpx; // 与热度字号统一
  color: #1f2937;
  line-height: 1.5;
}

.comment-time {
  font-size: 24rpx;
  color: #9ca3af;
}

.comment-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx;
  cursor: pointer;
  transition: opacity 0.3s ease;

  &:hover {
    opacity: 0.7;
  }
}

.more-text {
  font-size: 28rpx;
  color: #4f46e5;
}

.more-icon {
  font-size: 32rpx;
  color: #4f46e5;
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: "liga";
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

// 新闻区域
.news-section {
  background: white;
  padding: 32rpx;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.news-item {
  display: flex;
  align-items: center;
  gap: 32rpx;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.8;
  }
}

.news-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.news-title {
  font-size: 28rpx;
  color: #1f2937;
  line-height: 1.4;
  transition: color 0.3s ease;

  .news-item:hover & {
    color: #4f46e5;
  }
}

.news-source {
  font-size: 24rpx;
  color: #9ca3af;
}

.news-image {
  width: 192rpx;
  height: 128rpx;
  background-color: #e5e7eb;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  .image-text {
    color: #6b7280;
    font-size: 24rpx;
  }
}

// Cut区域
.cut-section {
  background: white;
  padding: 32rpx;
}

// Cut网格容器
.cut-grid-container {
  position: relative;
}

.cut-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx 24rpx;
}

.cut-item {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  cursor: pointer;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.02);
  }
}

.cut-cover {
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
  background-color: #e5e7eb;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;

  .cover-text {
    color: #6b7280;
    font-size: 28rpx;
  }
}

.cut-overlay {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  .play-icon {
    font-size: 80rpx;
    color: white;
    text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.5);
    font-family: "Material Icons";
    font-weight: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    font-feature-settings: "liga";
    -webkit-font-feature-settings: "liga";
    -webkit-font-smoothing: antialiased;
  }
}

.cut-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 整个Cut区域的付费解锁遮罩
.cut-area-pay-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8rpx);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;

  &:hover {
    background: rgba(0, 0, 0, 0.8);
  }
}

.pay-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  text-align: center;
  padding: 32rpx;

  .lock-icon {
    font-size: 80rpx;
    color: #fbbf24;
    font-family: "Material Icons";
    font-weight: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    font-feature-settings: "liga";
    -webkit-font-feature-settings: "liga";
    -webkit-font-smoothing: antialiased;
    margin-bottom: 8rpx;
  }

  .pay-text {
    font-size: 36rpx;
    color: white;
    font-weight: 700;
    margin-bottom: 8rpx;
  }

  .pay-subtitle {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
    line-height: 1.4;
  }
}

// 固定底部分享区域
.fixed-share-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx;
  background: white;
  border-top: 2rpx solid #f3f4f6;
  box-shadow: 0 -4rpx 12rpx 0 rgba(0, 0, 0, 0.1);
  z-index: 100;

  // 安全区域适配
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
}

.page-drama .share-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 16rpx 32rpx; // 减少内边距，降低按钮高度
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 40rpx; // 减少圆角
  cursor: pointer;
  transition: all 0.3s ease;
  width: 200rpx; // 限制按钮宽度
  margin: 0 auto; // 居中显示

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 20rpx 0 rgba(16, 185, 129, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

.share-icon {
  font-size: 28rpx; // 减小图标尺寸
  color: white;
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: "liga";
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.share-text {
  font-size: 28rpx; // 减小文字尺寸
  color: white;
  font-weight: 600;
}

// 评论弹窗
.comment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.comment-modal {
  width: 100%;
  max-width: 750rpx;
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  padding: 32rpx;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid #f3f4f6;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f3f4f6;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #e5e7eb;
  }

  .material-icons {
    font-size: 32rpx;
    color: #6b7280;
  }
}

.modal-content {
  margin-bottom: 32rpx;
}

.comment-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 32rpx;
  line-height: 1.5;
  resize: none;
  outline: none;
  background: #fafafa;

  &:focus {
    border-color: #6366f1;
    background: white;
  }
}

.comment-count {
  text-align: right;
  margin-top: 16rpx;
  font-size: 24rpx;
  color: #9ca3af;
}

.modal-footer {
  display: flex;
  gap: 24rpx;
}

.modal-cancel,
.modal-submit {
  flex: 1;
  padding: 20rpx;
  border-radius: 48rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-cancel {
  background: #f3f4f6;
  color: #6b7280;

  &:hover {
    background: #e5e7eb;
  }
}

.modal-submit {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 20rpx 0 rgba(99, 102, 241, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}
