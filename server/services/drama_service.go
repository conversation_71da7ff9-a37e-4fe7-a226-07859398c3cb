package services

import (
	"strconv"
	"strings"

	"github.com/jony4/52kanduanju.mp/server/config"
	"github.com/jony4/52kanduanju.mp/server/dao"
	"github.com/jony4/52kanduanju.mp/server/models"
	"gorm.io/gorm"
)

// DuanjuService 短剧服务 (新结构)
type DuanjuService struct {
	*BaseService
	duanjuDAO     *dao.DuanjuDAO
	duanjuNewsDAO *dao.DuanjuNewsDAO
	paymentDAO    *dao.PaymentDAO
}

func NewDuanjuService(db *gorm.DB, cfg *config.Config) *DuanjuService {
	return &DuanjuService{
		BaseService:   NewBaseService(db, cfg),
		duanjuDAO:     dao.NewDuanjuDAO(db),
		duanjuNewsDAO: dao.NewDuanjuNewsDAO(db),
		paymentDAO:    dao.NewPaymentDAO(db),
	}
}

// isColumnNotFoundError 检查是否是字段不存在的错误
func isColumnNotFoundError(err error) bool {
	if err == nil {
		return false
	}
	errMsg := strings.ToLower(err.Error())
	return strings.Contains(errMsg, "unknown column") ||
		strings.Contains(errMsg, "column") && strings.Contains(errMsg, "doesn't exist")
}

// GetDuanjuDetail 获取短剧详情
func (s *DuanjuService) GetDuanjuDetail(duanjuIDStr string) (*models.DuanjuResponse, error) {
	duanjuID, err := strconv.ParseUint(duanjuIDStr, 10, 64)
	if err != nil {
		return nil, err
	}

	duanju, err := s.duanjuDAO.GetByID(duanjuID)
	if err != nil {
		return nil, err
	}

	return s.ConvertDuanjuToResponse(duanju), nil
}

// GetDuanjuList 获取短剧列表
func (s *DuanjuService) GetDuanjuList(category, platform, sort string, page, limit int) (*models.DuanjuListResponse, error) {
	pagination := s.ValidatePagination(page, limit)

	params := &dao.DuanjuListParams{
		Category: category,
		Platform: platform,
		Sort:     sort,
	}

	duanjus, paginationResult, err := s.duanjuDAO.GetList(params, pagination)
	if err != nil {
		return nil, err
	}

	var duanjuResponses []models.DuanjuResponse
	for _, duanju := range duanjus {
		duanjuResponses = append(duanjuResponses, *s.ConvertDuanjuToResponse(&duanju))
	}

	return &models.DuanjuListResponse{
		Duanjus:    duanjuResponses,
		Pagination: s.ConvertPaginationResult(paginationResult),
	}, nil
}

// GetDuanjuSeriesCuts 获取短剧剧集精彩片段
func (s *DuanjuService) GetDuanjuSeriesCuts(duanjuIDStr string, userID uint64, page, limit int) (*models.DuanjuSeriesCutListResponse, error) {
	duanjuID, err := strconv.ParseUint(duanjuIDStr, 10, 64)
	if err != nil {
		return nil, err
	}

	pagination := s.ValidatePagination(page, limit)

	cuts, paginationResult, err := s.duanjuDAO.GetSeriesCuts(duanjuID, pagination)
	if err != nil {
		// 如果是字段不存在的错误，返回空结果而不是错误
		if isColumnNotFoundError(err) {
			return &models.DuanjuSeriesCutListResponse{
				Cuts:       []models.DuanjuSeriesCutResponse{},
				Pagination: s.ConvertPaginationResult(&dao.PaginationResult{Page: page, Limit: limit, Total: 0, TotalPages: 0}),
			}, nil
		}
		return nil, err
	}

	var cutResponses []models.DuanjuSeriesCutResponse
	for _, cut := range cuts {
		cutResponses = append(cutResponses, *s.ConvertDuanjuSeriesCutToResponse(&cut))
	}

	return &models.DuanjuSeriesCutListResponse{
		Cuts:       cutResponses,
		Pagination: s.ConvertPaginationResult(paginationResult),
	}, nil
}

// GetDuanjuNews 获取短剧相关新闻 (使用新的DuanjuNewsDAO)
func (s *DuanjuService) GetDuanjuNews(duanjuIDStr string) (*models.NewsListResponse, error) {
	duanjuID, err := strconv.ParseUint(duanjuIDStr, 10, 64)
	if err != nil {
		return nil, err
	}

	// 使用固定分页参数获取新闻
	pagination := &dao.PaginationParams{Page: 1, Limit: 20}
	news, paginationResult, err := s.duanjuNewsDAO.GetByDuanjuID(duanjuID, pagination)
	if err != nil {
		return nil, err
	}

	var newsResponses []models.NewsResponse
	for _, newsItem := range news {
		newsResponses = append(newsResponses, *s.ConvertDuanjuNewsToResponse(&newsItem))
	}

	return &models.NewsListResponse{
		News:       newsResponses,
		Pagination: s.ConvertPaginationResult(paginationResult),
	}, nil
}
