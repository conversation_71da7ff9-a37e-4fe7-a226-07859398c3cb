package dao

import (
	"github.com/jony4/52kanduanju.mp/server/models"
	"gorm.io/gorm"
)

// DuanjuDAO 短剧数据访问对象 (新结构)
type DuanjuDAO struct {
	*BaseDAO
}

func NewDuanjuDAO(db *gorm.DB) *DuanjuDAO {
	return &DuanjuDAO{
		BaseDAO: NewBaseDAO(db),
	}
}

// GetByID 根据ID获取短剧详情
func (d *DuanjuDAO) GetByID(id uint64) (*models.Duanju, error) {
	var duanju models.Duanju
	err := d.DB.Preload("Actors").Preload("Categories").Preload("Authors").
		Where("id = ? AND is_public = 1", id).First(&duanju).Error
	if err != nil {
		return nil, err
	}
	return &duanju, nil
}

// GetList 获取短剧列表
func (d *DuanjuDAO) GetList(params *DuanjuListParams, pagination *PaginationParams) ([]models.Duanju, *PaginationResult, error) {
	var duanjus []models.Duanju
	var total int64

	query := d.DB.Model(&models.Duanju{}).Where("duanju.is_public = 1")

	// 添加筛选条件
	if params.Category != "" {
		query = query.Joins("JOIN t_ofejlo6jzf9 dc ON duanju.id = dc.f_e6dqsc9q8j7").
			Joins("JOIN duanju_categories c ON dc.f_xeblmd9tsf8 = c.id").
			Where("c.name = ?", params.Category)
	}

	if params.Platform != "" {
		query = query.Where("duanju.platform = ?", params.Platform)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, nil, err
	}

	// 添加排序
	switch params.Sort {
	case "latest":
		query = query.Order("duanju.created_at DESC")
	case "rating":
		query = query.Order("duanju.heat_count DESC")
	default: // hot
		query = query.Order("duanju.read_count DESC")
	}

	// 分页
	offset := pagination.GetOffset()
	err := query.Preload("Actors").Preload("Categories").Preload("Authors").
		Offset(offset).Limit(pagination.Limit).Find(&duanjus).Error
	if err != nil {
		return nil, nil, err
	}

	paginationResult := NewPaginationResult(pagination.Page, pagination.Limit, total)
	return duanjus, paginationResult, nil
}

// GetSeriesCuts 获取短剧的剧集精彩片段
func (d *DuanjuDAO) GetSeriesCuts(duanjuID uint64, pagination *PaginationParams) ([]models.DuanjuSeriesCut, *PaginationResult, error) {
	var cuts []models.DuanjuSeriesCut
	var total int64

	// 暂时返回空结果，因为数据库表缺少duanju_id字段
	// TODO: 需要添加duanju_id字段到duanju_series_cuts表，或者通过其他方式关联
	// query := d.DB.Model(&models.DuanjuSeriesCut{}).Where("duanju_id = ?", duanjuID)

	// 返回空结果
	total = 0
	cuts = []models.DuanjuSeriesCut{}

	paginationResult := NewPaginationResult(pagination.Page, pagination.Limit, total)
	return cuts, paginationResult, nil
}

// GetRecommendations 获取推荐短剧
func (d *DuanjuDAO) GetRecommendations(excludeID uint64, limit int) ([]models.Duanju, error) {
	var duanjus []models.Duanju
	query := d.DB.Where("is_public = 1")
	if excludeID != 0 {
		query = query.Where("id != ?", excludeID)
	}
	err := query.Preload("Actors").Preload("Categories").Preload("Authors").
		Order("read_count DESC").Limit(limit).Find(&duanjus).Error
	return duanjus, err
}

// GetHotDuanjus 获取热门短剧
func (d *DuanjuDAO) GetHotDuanjus(limit int) ([]models.Duanju, error) {
	var duanjus []models.Duanju
	err := d.DB.Where("is_public = 1").Preload("Actors").Preload("Categories").
		Order("read_count DESC").Limit(limit).Find(&duanjus).Error
	return duanjus, err
}

// Search 搜索短剧
func (d *DuanjuDAO) Search(keyword string, pagination *PaginationParams) ([]models.Duanju, *PaginationResult, error) {
	var duanjus []models.Duanju
	var total int64

	query := d.DB.Model(&models.Duanju{}).Where("is_public = 1 AND book_name LIKE ?", "%"+keyword+"%")

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, nil, err
	}

	// 分页查询
	offset := pagination.GetOffset()
	err := query.Preload("Actors").Preload("Categories").
		Order("read_count DESC").
		Offset(offset).Limit(pagination.Limit).Find(&duanjus).Error
	if err != nil {
		return nil, nil, err
	}

	paginationResult := NewPaginationResult(pagination.Page, pagination.Limit, total)
	return duanjus, paginationResult, nil
}

// GetByCategoryName 根据分类名获取短剧列表
func (d *DuanjuDAO) GetByCategoryName(categoryName string, pagination *PaginationParams) ([]models.Duanju, *PaginationResult, error) {
	var duanjus []models.Duanju
	var total int64

	query := d.DB.Model(&models.Duanju{}).
		Joins("JOIN t_ofejlo6jzf9 dc ON duanju.id = dc.f_e6dqsc9q8j7").
		Joins("JOIN duanju_categories c ON dc.f_xeblmd9tsf8 = c.id").
		Where("duanju.is_public = 1 AND c.name = ?", categoryName)

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, nil, err
	}

	// 分页查询
	offset := pagination.GetOffset()
	err := query.Preload("Actors").Preload("Categories").
		Order("duanju.read_count DESC").
		Offset(offset).Limit(pagination.Limit).Find(&duanjus).Error
	if err != nil {
		return nil, nil, err
	}

	paginationResult := NewPaginationResult(pagination.Page, pagination.Limit, total)
	return duanjus, paginationResult, nil
}

// DuanjuListParams 短剧列表查询参数
type DuanjuListParams struct {
	Category string
	Platform string
	Sort     string // hot, latest, rating
}
